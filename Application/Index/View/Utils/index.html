<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover">
    <title>卡密购买 - {$con.webname}</title>
    <link rel="icon" href="__PUBLIC__/static/annie/img/fovicon.ico">
    <meta name="description" content="购买会员卡密，享受更多服务">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
            background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 50%, #fff0f5 100%);
            background-attachment: fixed;
            min-height: 100vh;
            padding: 20px;
            position: relative;
            image-rendering: pixelated;
            -ms-interpolation-mode: nearest-neighbor;
            margin: 0;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, rgba(255, 182, 193, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 40%, rgba(173, 216, 230, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(221, 160, 221, 0.3) 0%, transparent 50%),
                repeating-linear-gradient(
                    0deg,
                    transparent,
                    transparent 2px,
                    rgba(255, 255, 255, 0.03) 2px,
                    rgba(255, 255, 255, 0.03) 4px
                );
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 800px;
            width: 100%;
            margin: 0;
            background: rgba(255, 255, 255, 0.9);
            border: 3px solid #87ceeb;
            border-radius: 0;
            box-shadow:
                8px 8px 0 rgba(135, 206, 235, 0.3),
                0 0 0 1px #ffffff,
                inset 2px 2px 0 rgba(255, 255, 255, 0.8),
                inset -2px -2px 0 rgba(0, 0, 0, 0.1);
            overflow: hidden;
            image-rendering: pixelated;
        }

        .header {
            background: linear-gradient(135deg, #ffb6c1 0%, #ffc0cb 50%, #dda0dd 100%);
            border-bottom: 3px solid #ff69b4;
            color: #2f4f4f;
            padding: 30px;
            text-align: center;
            position: relative;
            box-shadow: inset 2px 2px 0 rgba(255, 255, 255, 0.5);
        }

        .header h1 {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 0 rgba(255, 255, 255, 0.8);
            letter-spacing: 2px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.8;
            font-weight: normal;
        }

        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: #ffffff;
            border: 2px solid #87ceeb;
            color: #2f4f4f;
            padding: 8px 16px;
            border-radius: 0;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            font-size: 14px;
            font-weight: bold;
            box-shadow:
                2px 2px 0 #87ceeb,
                inset 1px 1px 0 rgba(255, 255, 255, 0.8);
        }

        .back-btn:hover {
            background: #f0f8ff;
            transform: translate(-1px, -1px);
            box-shadow:
                3px 3px 0 #87ceeb,
                inset 1px 1px 0 rgba(255, 255, 255, 0.9);
        }

        .back-btn:active {
            transform: translate(1px, 1px);
            box-shadow:
                1px 1px 0 #87ceeb,
                inset 1px 1px 0 rgba(0, 0, 0, 0.1);
        }

        .products {
            padding: 40px;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .product-card {
            background: #ffffff;
            border: 3px solid #98fb98;
            border-radius: 0;
            padding: 24px;
            box-shadow:
                4px 4px 0 rgba(152, 251, 152, 0.5),
                inset 2px 2px 0 rgba(255, 255, 255, 0.8),
                inset -1px -1px 0 rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: repeating-linear-gradient(
                90deg,
                #ff69b4 0px,
                #ff69b4 8px,
                #87ceeb 8px,
                #87ceeb 16px,
                #98fb98 16px,
                #98fb98 24px
            );
            animation: pixelMove 2s linear infinite;
        }

        @keyframes pixelMove {
            0% { background-position: 0 0; }
            100% { background-position: 24px 0; }
        }

        .product-card:hover {
            transform: translate(-2px, -2px);
            box-shadow:
                6px 6px 0 rgba(152, 251, 152, 0.7),
                inset 2px 2px 0 rgba(255, 255, 255, 0.9),
                inset -1px -1px 0 rgba(0, 0, 0, 0.15);
            border-color: #90ee90;
        }

        .product-title {
            font-size: 1.4em;
            font-weight: bold;
            color: #2f4f4f;
            margin-bottom: 15px;
            text-align: center;
            text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.8);
        }

        .product-price {
            font-size: 1.8em;
            font-weight: bold;
            color: #ff1493;
            text-align: center;
            margin-bottom: 15px;
            text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.8);
        }

        .product-price .currency {
            font-size: 0.6em;
            vertical-align: top;
        }

        .product-features {
            list-style: none;
            margin-bottom: 25px;
        }

        .product-features li {
            padding: 8px 0;
            color: #666;
            position: relative;
            padding-left: 25px;
        }

        .product-features li::before {
            content: '★';
            position: absolute;
            left: 0;
            color: #ffd700;
            font-weight: bold;
            text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.3);
        }

        .buy-btn {
            width: 100%;
            background: linear-gradient(135deg, #ff69b4 0%, #ff1493 100%);
            color: white;
            border: 2px solid #ff1493;
            padding: 12px 24px;
            border-radius: 0;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow:
                3px 3px 0 rgba(255, 20, 147, 0.5),
                inset 1px 1px 0 rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
            text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.3);
        }

        .buy-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: repeating-linear-gradient(
                45deg,
                transparent 0px,
                transparent 2px,
                rgba(255, 255, 255, 0.1) 2px,
                rgba(255, 255, 255, 0.1) 4px
            );
            transition: left 0.3s ease;
        }

        .buy-btn:hover::before {
            left: 100%;
        }

        .buy-btn:hover {
            transform: translate(-1px, -1px);
            box-shadow:
                4px 4px 0 rgba(255, 20, 147, 0.7),
                inset 1px 1px 0 rgba(255, 255, 255, 0.4);
        }

        .buy-btn:active {
            transform: translate(1px, 1px);
            box-shadow:
                2px 2px 0 rgba(255, 20, 147, 0.5),
                inset 1px 1px 0 rgba(0, 0, 0, 0.1);
        }

        .popular {
            position: relative;
        }

        .popular::after {
            content: '★ HOT ★';
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ffd700;
            color: #ff4500;
            border: 2px solid #ff4500;
            padding: 4px 12px;
            border-radius: 0;
            font-size: 11px;
            font-weight: bold;
            transform: rotate(15deg);
            box-shadow:
                2px 2px 0 rgba(255, 69, 0, 0.5),
                inset 1px 1px 0 rgba(255, 255, 255, 0.8);
            animation: pixelBlink 1.5s ease-in-out infinite;
            text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.8);
        }

        @keyframes pixelBlink {
            0%, 50%, 100% { opacity: 1; }
            25%, 75% { opacity: 0.7; }
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
        }

        .modal-content {
            background: #ffffff;
            border: 3px solid #87ceeb;
            margin: 5% auto;
            padding: 0;
            border-radius: 0;
            width: 90%;
            max-width: 500px;
            box-shadow:
                8px 8px 0 rgba(135, 206, 235, 0.5),
                inset 2px 2px 0 rgba(255, 255, 255, 0.8),
                inset -1px -1px 0 rgba(0, 0, 0, 0.1);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #ffb6c1 0%, #ffc0cb 100%);
            border-bottom: 2px solid #ff69b4;
            color: #2f4f4f;
            padding: 20px;
            border-radius: 0;
            position: relative;
            box-shadow: inset 1px 1px 0 rgba(255, 255, 255, 0.8);
        }

        .modal-header h2 {
            margin: 0;
            text-align: center;
        }

        .close {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #2f4f4f;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0;
            background: #ffffff;
            border: 2px solid #ff69b4;
            transition: all 0.2s ease;
            box-shadow: 1px 1px 0 rgba(255, 105, 180, 0.5);
        }

        .close:hover {
            background: #f0f8ff;
            transform: translateY(-50%) translate(-1px, -1px);
            box-shadow: 2px 2px 0 rgba(255, 105, 180, 0.7);
        }

        .close:active {
            transform: translateY(-50%) translate(1px, 1px);
            box-shadow: 0px 0px 0 rgba(255, 105, 180, 0.5);
        }

        .modal-body {
            padding: 30px;
            text-align: center;
        }

        .payment-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 25px;
        }

        .payment-option {
            padding: 12px;
            border: 2px solid #98fb98;
            border-radius: 0;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            background: #ffffff;
            box-shadow:
                2px 2px 0 rgba(152, 251, 152, 0.5),
                inset 1px 1px 0 rgba(255, 255, 255, 0.8);
        }

        .payment-option:hover {
            border-color: #90ee90;
            background-color: #f0fff0;
            transform: translate(-1px, -1px);
            box-shadow:
                3px 3px 0 rgba(152, 251, 152, 0.7),
                inset 1px 1px 0 rgba(255, 255, 255, 0.9);
        }

        .payment-option.selected {
            border-color: #ff69b4;
            background-color: #ff69b4;
            color: white;
            box-shadow:
                2px 2px 0 rgba(255, 105, 180, 0.5),
                inset 1px 1px 0 rgba(255, 255, 255, 0.3);
        }

        .payment-icon {
            width: 24px;
            height: 24px;
        }

        .qr-container {
            margin: 20px 0;
        }

        .qr-code {
            display: inline-block;
            padding: 16px;
            background: white;
            border: 3px solid #4169e1;
            border-radius: 0;
            box-shadow:
                4px 4px 0 rgba(65, 105, 225, 0.3),
                inset 2px 2px 0 rgba(255, 255, 255, 0.8);
        }

        .payment-info {
            margin: 20px 0;
            padding: 12px;
            background: #f0fff0;
            border: 2px solid #32cd32;
            border-radius: 0;
            box-shadow:
                2px 2px 0 rgba(50, 205, 50, 0.3),
                inset 1px 1px 0 rgba(255, 255, 255, 0.8);
        }

        .confirm-btn {
            background: linear-gradient(135deg, #32cd32 0%, #228b22 100%);
            color: white;
            border: 2px solid #228b22;
            padding: 12px 24px;
            border-radius: 0;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 20px;
            box-shadow:
                3px 3px 0 rgba(34, 139, 34, 0.5),
                inset 1px 1px 0 rgba(255, 255, 255, 0.3);
            text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.3);
        }

        .confirm-btn:hover {
            transform: translate(-1px, -1px);
            box-shadow:
                4px 4px 0 rgba(34, 139, 34, 0.7),
                inset 1px 1px 0 rgba(255, 255, 255, 0.4);
        }

        .confirm-btn:active {
            transform: translate(1px, 1px);
            box-shadow:
                2px 2px 0 rgba(34, 139, 34, 0.5),
                inset 1px 1px 0 rgba(0, 0, 0, 0.1);
        }

        .confirm-btn:disabled {
            background: #cccccc;
            border-color: #999999;
            cursor: not-allowed;
            transform: none;
            box-shadow:
                2px 2px 0 rgba(153, 153, 153, 0.5),
                inset 1px 1px 0 rgba(255, 255, 255, 0.3);
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .success-icon {
            color: #4CAF50;
            font-size: 3em;
            margin-bottom: 20px;
        }

        .error-icon {
            color: #f44336;
            font-size: 3em;
            margin-bottom: 20px;
        }

        .token-display {
            background: #f0f8ff;
            border: 3px solid #4169e1;
            border-radius: 0;
            padding: 16px;
            margin: 20px 0;
            font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
            font-size: 1em;
            font-weight: bold;
            color: #191970;
            word-break: break-all;
            position: relative;
            box-shadow:
                4px 4px 0 rgba(65, 105, 225, 0.3),
                inset 2px 2px 0 rgba(255, 255, 255, 0.8),
                inset -1px -1px 0 rgba(0, 0, 0, 0.1);
        }

        .copy-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #ffffff;
            border: 2px solid #87ceeb;
            color: #2f4f4f;
            padding: 6px 10px;
            border-radius: 0;
            cursor: pointer;
            font-size: 11px;
            font-weight: bold;
            transition: all 0.2s ease;
            box-shadow:
                2px 2px 0 rgba(135, 206, 235, 0.5),
                inset 1px 1px 0 rgba(255, 255, 255, 0.8);
        }

        .copy-btn:hover {
            background: #f0f8ff;
            transform: translate(-1px, -1px);
            box-shadow:
                3px 3px 0 rgba(135, 206, 235, 0.7),
                inset 1px 1px 0 rgba(255, 255, 255, 0.9);
        }

        .copy-btn:active {
            transform: translate(1px, 1px);
            box-shadow:
                1px 1px 0 rgba(135, 206, 235, 0.5),
                inset 1px 1px 0 rgba(0, 0, 0, 0.1);
        }

        .copy-btn.copied {
            background: #90ee90;
            border-color: #32cd32;
            color: #006400;
            box-shadow:
                2px 2px 0 rgba(50, 205, 50, 0.5),
                inset 1px 1px 0 rgba(255, 255, 255, 0.8);
        }

        .order-id-container {
            position: relative;
            display: inline-block;
        }

        .order-copy-btn {
            margin-left: 8px;
            background: #ffffff;
            border: 2px solid #87ceeb;
            color: #2f4f4f;
            padding: 3px 8px;
            border-radius: 0;
            cursor: pointer;
            font-size: 10px;
            font-weight: bold;
            transition: all 0.2s ease;
            box-shadow:
                1px 1px 0 rgba(135, 206, 235, 0.5),
                inset 1px 1px 0 rgba(255, 255, 255, 0.8);
        }

        .order-copy-btn:hover {
            background: #f0f8ff;
            transform: translate(-1px, -1px);
            box-shadow:
                2px 2px 0 rgba(135, 206, 235, 0.7),
                inset 1px 1px 0 rgba(255, 255, 255, 0.9);
        }

        .order-copy-btn:active {
            transform: translate(1px, 1px);
            box-shadow:
                0px 0px 0 rgba(135, 206, 235, 0.5),
                inset 1px 1px 0 rgba(0, 0, 0, 0.1);
        }

        .contact-service-btn {
            background: linear-gradient(135deg, #ff6347 0%, #ff4500 100%);
            color: white;
            border: 2px solid #ff4500;
            padding: 10px 20px;
            border-radius: 0;
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 16px;
            box-shadow:
                3px 3px 0 rgba(255, 69, 0, 0.5),
                inset 1px 1px 0 rgba(255, 255, 255, 0.3);
            text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.3);
        }

        .contact-service-btn:hover {
            transform: translate(-1px, -1px);
            box-shadow:
                4px 4px 0 rgba(255, 69, 0, 0.7),
                inset 1px 1px 0 rgba(255, 255, 255, 0.4);
        }

        .contact-service-btn:active {
            transform: translate(1px, 1px);
            box-shadow:
                2px 2px 0 rgba(255, 69, 0, 0.5),
                inset 1px 1px 0 rgba(0, 0, 0, 0.1);
        }

        .telegram-link:hover {
            background: #cce7ff !important;
            transform: translate(-1px, -1px);
            box-shadow: 4px 4px 0 rgba(0, 136, 204, 0.5), inset 1px 1px 0 rgba(255, 255, 255, 0.9) !important;
        }

        .telegram-link:active {
            transform: translate(1px, 1px);
            box-shadow: 2px 2px 0 rgba(0, 136, 204, 0.3), inset 1px 1px 0 rgba(0, 0, 0, 0.1) !important;
        }

        .warning-text {
            color: #8b0000;
            font-size: 0.9em;
            margin-top: 15px;
            padding: 12px;
            background: #ffe4e1;
            border: 2px solid #ff6347;
            border-radius: 0;
            font-weight: bold;
            box-shadow:
                2px 2px 0 rgba(255, 99, 71, 0.3),
                inset 1px 1px 0 rgba(255, 255, 255, 0.8);
        }

        /* 平板设备适配 */
        @media (max-width: 1024px) {
            .container {
                max-width: 90%;
                margin: 15px auto;
            }

            .product-grid {
                grid-template-columns: 1fr 1fr;
                gap: 20px;
            }
        }

        /* 手机横屏适配 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
                margin: 0;
            }

            .container {
                margin: 0;
                border-radius: 0;
                border-width: 2px;
                box-shadow:
                    4px 4px 0 rgba(135, 206, 235, 0.3),
                    inset 1px 1px 0 rgba(255, 255, 255, 0.8);
            }

            .header {
                padding: 16px;
                position: relative;
            }

            .header h1 {
                font-size: 1.8em;
                margin-bottom: 8px;
                line-height: 1.2;
            }

            .header p {
                font-size: 1em;
                line-height: 1.4;
            }

            .back-btn {
                top: 12px;
                left: 12px;
                padding: 6px 12px;
                font-size: 12px;
            }

            .products {
                padding: 16px;
            }

            .product-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .product-card {
                padding: 20px;
            }

            .product-title {
                font-size: 1.3em;
                margin-bottom: 12px;
            }

            .product-price {
                font-size: 1.6em;
                margin-bottom: 12px;
            }

            .product-features {
                margin-bottom: 20px;
            }

            .product-features li {
                padding: 6px 0;
                font-size: 0.9em;
            }

            .buy-btn {
                padding: 10px 20px;
                font-size: 1em;
            }

            .popular::after {
                top: -6px;
                right: -6px;
                padding: 4px 10px;
                font-size: 10px;
            }

            .modal-content {
                width: 95%;
                margin: 5% auto;
                max-height: 90vh;
                overflow-y: auto;
            }

            .modal-header {
                padding: 16px;
            }

            .modal-header h2 {
                font-size: 1.3em;
            }

            .modal-body {
                padding: 20px;
            }

            .payment-options {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .payment-option {
                padding: 10px;
                font-size: 0.9em;
            }

            .payment-info {
                padding: 10px;
                font-size: 0.9em;
                margin: 16px 0;
            }

            .confirm-btn {
                padding: 10px 20px;
                font-size: 1em;
                margin-top: 16px;
            }

            .qr-code {
                padding: 12px;
            }

            .token-display {
                padding: 12px;
                font-size: 0.9em;
                margin: 16px 0;
                word-break: break-all;
                line-height: 1.4;
            }

            .copy-btn {
                top: 6px;
                right: 6px;
                padding: 4px 8px;
                font-size: 10px;
            }

            .order-copy-btn {
                padding: 2px 6px;
                font-size: 9px;
                margin-left: 6px;
            }

            .contact-service-btn {
                padding: 8px 16px;
                font-size: 0.9em;
                margin-top: 12px;
            }

            .warning-text {
                padding: 10px;
                font-size: 0.8em;
                margin-top: 12px;
                line-height: 1.4;
            }

            .close {
                width: 20px;
                height: 20px;
                font-size: 16px;
                right: 10px;
            }
        }

        /* 小屏手机适配 */
        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.5em;
                line-height: 1.3;
            }

            .header p {
                font-size: 0.9em;
                line-height: 1.5;
            }

            .product-card {
                padding: 16px;
                margin-bottom: 12px;
            }

            .product-title {
                font-size: 1.2em;
                line-height: 1.4;
            }

            .product-price {
                font-size: 1.4em;
                line-height: 1.2;
            }

            .product-features {
                margin-bottom: 18px;
            }

            .product-features li {
                font-size: 0.85em;
                padding: 5px 0;
                line-height: 1.4;
            }

            .buy-btn {
                padding: 10px 16px;
                font-size: 0.95em;
                line-height: 1.2;
                min-height: 40px;
            }

            .modal-content {
                width: 98%;
                margin: 2% auto;
                border-width: 2px;
            }

            .modal-header {
                padding: 12px;
            }

            .modal-header h2 {
                font-size: 1.2em;
                line-height: 1.3;
            }

            .modal-body {
                padding: 16px;
                line-height: 1.5;
            }

            .payment-option {
                padding: 10px 8px;
                font-size: 0.85em;
                line-height: 1.3;
                min-height: 40px;
            }

            .payment-info {
                font-size: 0.85em;
                line-height: 1.4;
                padding: 8px;
            }

            .token-display {
                font-size: 0.8em;
                padding: 10px;
                line-height: 1.5;
            }

            .copy-btn {
                padding: 4px 8px;
                font-size: 9px;
                min-height: 24px;
                min-width: 32px;
            }

            .contact-service-btn {
                padding: 8px 12px;
                font-size: 0.85em;
                line-height: 1.2;
                min-height: 36px;
            }

            .warning-text {
                font-size: 0.75em;
                line-height: 1.5;
                padding: 8px;
            }

            .qr-container {
                margin: 16px 0;
                text-align: center;
            }

            .qr-code {
                padding: 8px;
                display: inline-block;
            }

            /* 优化联系客服模态框 */
            .telegram-link {
                padding: 10px 16px !important;
                font-size: 0.85em !important;
                line-height: 1.3 !important;
                min-height: 40px;
            }
        }

        /* 超小屏设备适配 */
        @media (max-width: 360px) {
            body {
                padding: 5px;
                font-size: 14px;
            }

            .container {
                margin: 0;
                border-width: 1px;
            }

            .header {
                padding: 10px;
            }

            .header h1 {
                font-size: 1.25em;
                line-height: 1.2;
                margin-bottom: 6px;
            }

            .header p {
                font-size: 0.8em;
                line-height: 1.4;
            }

            .back-btn {
                padding: 4px 8px;
                font-size: 10px;
                top: 8px;
                left: 8px;
                min-height: 28px;
            }

            .products {
                padding: 10px;
            }

            .product-card {
                padding: 10px;
                margin-bottom: 10px;
            }

            .product-title {
                font-size: 1.1em;
                margin-bottom: 8px;
            }

            .product-price {
                font-size: 1.3em;
                margin-bottom: 8px;
            }

            .product-features li {
                font-size: 0.8em;
                padding: 4px 0;
            }

            .buy-btn {
                padding: 8px 12px;
                font-size: 0.9em;
                min-height: 36px;
            }

            .modal-content {
                width: 100%;
                margin: 0;
                border-radius: 0;
                max-height: 100vh;
                border-width: 0;
            }

            .modal-header {
                padding: 10px;
            }

            .modal-header h2 {
                font-size: 1.1em;
            }

            .modal-body {
                padding: 10px;
                font-size: 0.9em;
            }

            .payment-option {
                padding: 8px;
                font-size: 0.8em;
                min-height: 36px;
            }

            .payment-info {
                padding: 6px;
                font-size: 0.8em;
                margin: 12px 0;
            }

            .confirm-btn {
                padding: 8px 16px;
                font-size: 0.9em;
                min-height: 36px;
            }

            .token-display {
                font-size: 0.75em;
                padding: 8px;
            }

            .copy-btn {
                padding: 3px 6px;
                font-size: 8px;
                min-height: 20px;
                min-width: 28px;
            }

            .order-copy-btn {
                padding: 2px 4px;
                font-size: 8px;
                margin-left: 4px;
            }

            .contact-service-btn {
                padding: 6px 10px;
                font-size: 0.8em;
                min-height: 32px;
            }

            .warning-text {
                font-size: 0.7em;
                padding: 6px;
            }

            .close {
                width: 24px;
                height: 24px;
                font-size: 14px;
                right: 8px;
            }

            .telegram-link {
                padding: 8px 12px !important;
                font-size: 0.8em !important;
                min-height: 36px;
            }

            .qr-code {
                padding: 6px;
            }
        }

        /* 触摸设备优化 */
        @media (hover: none) and (pointer: coarse) {
            .buy-btn, .confirm-btn, .contact-service-btn {
                min-height: 44px; /* iOS推荐的最小触摸目标 */
                padding: 12px 20px;
            }

            .payment-option {
                min-height: 44px;
                padding: 12px;
            }

            .copy-btn, .order-copy-btn {
                min-height: 32px;
                min-width: 32px;
                padding: 6px 10px;
            }

            .close {
                min-height: 44px;
                min-width: 44px;
            }

            .back-btn {
                min-height: 36px;
                padding: 8px 16px;
            }

            /* 移除hover效果，使用active效果 */
            .buy-btn:hover, .confirm-btn:hover, .contact-service-btn:hover {
                transform: none;
                box-shadow:
                    3px 3px 0 rgba(255, 20, 147, 0.5),
                    inset 1px 1px 0 rgba(255, 255, 255, 0.3);
            }

            .buy-btn:active, .confirm-btn:active, .contact-service-btn:active {
                transform: translate(1px, 1px);
                box-shadow:
                    2px 2px 0 rgba(255, 20, 147, 0.5),
                    inset 1px 1px 0 rgba(0, 0, 0, 0.1);
            }

            .product-card:hover {
                transform: none;
                box-shadow:
                    4px 4px 0 rgba(152, 251, 152, 0.5),
                    inset 2px 2px 0 rgba(255, 255, 255, 0.8),
                    inset -1px -1px 0 rgba(0, 0, 0, 0.1);
            }
        }

        /* 横屏模式优化 */
        @media (max-width: 768px) and (orientation: landscape) {
            .modal-content {
                max-height: 85vh;
                overflow-y: auto;
            }

            .header {
                padding: 12px 16px;
            }

            .header h1 {
                font-size: 1.6em;
            }

            .products {
                padding: 12px 16px;
            }

            .product-grid {
                grid-template-columns: 1fr 1fr;
                gap: 12px;
            }

            .product-card {
                padding: 16px;
            }
        }

        /* 高分辨率屏幕优化 */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .container, .modal-content, .product-card {
                border-width: 1px;
            }

            .buy-btn, .confirm-btn, .contact-service-btn {
                border-width: 1px;
            }

            .copy-btn, .order-copy-btn {
                border-width: 1px;
            }
        }

        /* iOS Safari 特殊优化 */
        @supports (-webkit-touch-callout: none) {
            body {
                -webkit-text-size-adjust: 100%;
                -webkit-tap-highlight-color: transparent;
            }

            .buy-btn, .confirm-btn, .contact-service-btn, .payment-option {
                -webkit-appearance: none;
                appearance: none;
                -webkit-tap-highlight-color: transparent;
                -webkit-touch-callout: none;
                -webkit-user-select: none;
                user-select: none;
            }

            .modal {
                -webkit-overflow-scrolling: touch;
            }

            .modal-content {
                -webkit-overflow-scrolling: touch;
            }

            /* 修复iOS Safari中的100vh问题 */
            @media (max-width: 768px) {
                .modal {
                    height: -webkit-fill-available;
                }
            }
        }

        /* 安全区域适配（刘海屏等） - 仅在需要时应用 */
        @supports (padding: max(0px)) {
            /* 只在横屏或特殊设备上应用安全区域 */
            @media (max-width: 768px) and (orientation: landscape) {
                body {
                    padding-left: max(10px, env(safe-area-inset-left));
                    padding-right: max(10px, env(safe-area-inset-right));
                }
            }

            /* 仅在底部应用安全区域，避免影响左右对齐 */
            body {
                padding-bottom: max(20px, env(safe-area-inset-bottom));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="/" class="back-btn">← 返回首页</a>
            <h1>🎫 会员卡密购买</h1>
            <p>选择适合您的会员套餐，享受更多优质服务</p>
        </div>

        <div class="products">
            <div class="product-grid">
                <!-- 月度卡密 -->
                <div class="product-card">
                    <div class="product-title">月度会员卡密</div>
                    <div class="product-price">
                        <span class="currency">¥</span>28.05
                    </div>
                    <ul class="product-features">
                        <li>买一月送一周</li>
                        <li>共计38天使用期</li>
                        <li>享受所有会员功能</li>
                        <li>优先客服支持</li>
                    </ul>
                    <button class="buy-btn" onclick="selectProduct(85, '月度会员卡密', '28.05')">立即购买</button>
                </div>

                <!-- 永久卡密 -->
                <div class="product-card popular">
                    <div class="product-title">永久会员卡密</div>
                    <div class="product-price">
                        <span class="currency">¥</span>38.05
                    </div>
                    <ul class="product-features">
                        <li>加10元得永久</li>
                        <li>终身使用权限</li>
                        <li>永不到期</li>
                        <li>所有功能无限制</li>
                        <li>VIP专属客服</li>
                    </ul>
                    <button class="buy-btn" onclick="selectProduct(86, '永久会员卡密', '38.05')">立即购买</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 支付方式选择模态框 -->
    <div id="paymentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>选择支付方式</h2>
                <span class="close" onclick="closeModal('paymentModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="payment-info">
                    <strong id="selectedProduct"></strong><br>
                    价格: ¥<span id="selectedPrice"></span>
                </div>

                <div class="payment-options">
                    <div class="payment-option" onclick="selectPayment('wxpay')">
                        <span class="payment-icon">💬</span>
                        <span>微信支付</span>
                    </div>
                    <div class="payment-option" onclick="selectPayment('alipay')">
                        <span class="payment-icon">💰</span>
                        <span>支付宝</span>
                    </div>
                </div>

                <button class="confirm-btn" id="confirmPayment" onclick="createOrder()" disabled>
                    确认支付方式
                </button>
            </div>
        </div>
    </div>

    <!-- 支付二维码模态框 -->
    <div id="qrModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>扫码支付</h2>
                <span class="close" onclick="closeModal('qrModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="payment-info">
                    <strong>订单号:
                        <span class="order-id-container">
                            <span id="orderId"></span>
                            <button class="order-copy-btn" onclick="copyCurrentOrderId()">复制</button>
                        </span>
                    </strong><br>
                    商品: <span id="orderProduct"></span><br>
                    金额: ¥<span id="orderPrice"></span>
                </div>

                <div class="qr-container">
                    <div class="qr-code" id="qrCode"></div>
                </div>

                <p>请使用<span id="paymentMethod"></span>扫描上方二维码完成支付</p>

                <button class="confirm-btn" id="checkPayment" onclick="checkPaymentStatus()">
                    <span id="checkText">我已支付</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 支付结果模态框 -->
    <div id="resultModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="resultTitle">支付结果</h2>
                <span class="close" onclick="closeModal('resultModal')">&times;</span>
            </div>
            <div class="modal-body" id="resultBody">
                <!-- 动态内容 -->
            </div>
        </div>
    </div>

    <!-- 联系客服模态框 -->
    <div id="contactModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>📞 联系客服</h2>
                <span class="close" onclick="closeModal('contactModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div style="text-align: center;">
                    <div style="font-size: 3em; margin-bottom: 20px;">🎧</div>
                    <h3 style="margin-bottom: 20px; color: #333;">需要帮助？联系我们的客服团队</h3>

                    <div style="display: grid; gap: 16px; margin: 24px 0;">
                        <a href="https://t.me/Dataso" target="_blank"
                           class="telegram-link"
                           style="display: flex; align-items: center; justify-content: center; gap: 12px;
                                  padding: 12px 20px; background: #e6f3ff;
                                  border: 2px solid #0088cc; border-radius: 0;
                                  color: #0088cc; text-decoration: none; font-weight: bold;
                                  transition: all 0.2s ease;
                                  box-shadow: 3px 3px 0 rgba(0, 136, 204, 0.3), inset 1px 1px 0 rgba(255, 255, 255, 0.8);">
                            <span style="font-size: 1.5em;">📱</span>
                            <span>Telegram客服: @Dataso</span>
                        </a>

                        <div style="display: flex; align-items: center; justify-content: center; gap: 12px;
                                    padding: 12px 20px; background: #f0f8ff;
                                    border: 2px solid #87ceeb; border-radius: 0;
                                    color: #4682b4; font-weight: bold;
                                    box-shadow: 2px 2px 0 rgba(135, 206, 235, 0.3), inset 1px 1px 0 rgba(255, 255, 255, 0.8);">
                            <span style="font-size: 1.5em;">⏰</span>
                            <span>服务时间: 9:00-23:00</span>
                        </div>
                    </div>

                    <div style="background: #fffacd; border: 2px solid #ffd700;
                                border-radius: 0; padding: 12px; margin-top: 20px; color: #b8860b; font-weight: bold;
                                box-shadow: 2px 2px 0 rgba(255, 215, 0, 0.3), inset 1px 1px 0 rgba(255, 255, 255, 0.8);">
                        <strong>💡 温馨提示：</strong><br>
                        联系客服时请提供您的订单号，以便我们更快地为您解决问题。
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入二维码生成库 -->
    <script src="__PUBLIC__/js/qrcode.js"></script>
    <script>
        let currentProduct = null;
        let currentPaymentType = null;
        let currentOrderId = null;
        let checkingPayment = false;
        let orderProcessed = false; // 防止重复处理标记

        // 重置订单状态
        function resetOrderStatus() {
            currentOrderId = null;
            checkingPayment = false;
            orderProcessed = false;

            // 重置按钮状态
            const checkBtn = document.getElementById('checkPayment');
            if (checkBtn) {
                checkBtn.disabled = false;
                checkBtn.style.display = 'block';
                document.getElementById('checkText').textContent = '我已支付';
            }
        }

        // 选择商品
        function selectProduct(productId, productName, price) {
            // 重置之前的订单状态
            resetOrderStatus();

            currentProduct = {
                id: productId,
                name: productName,
                price: price
            };

            document.getElementById('selectedProduct').textContent = productName;
            document.getElementById('selectedPrice').textContent = price;

            // 重置支付方式选择
            currentPaymentType = null;
            document.querySelectorAll('.payment-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.getElementById('confirmPayment').disabled = true;

            showModal('paymentModal');
        }

        // 选择支付方式
        function selectPayment(paymentType) {
            currentPaymentType = paymentType;

            document.querySelectorAll('.payment-option').forEach(option => {
                option.classList.remove('selected');
            });

            event.target.closest('.payment-option').classList.add('selected');
            document.getElementById('confirmPayment').disabled = false;
        }

        // 创建订单
        function createOrder() {
            if (!currentProduct || !currentPaymentType) {
                alert('请选择商品和支付方式');
                return;
            }

            const confirmBtn = document.getElementById('confirmPayment');
            confirmBtn.disabled = true;
            confirmBtn.innerHTML = '<span class="loading"></span>创建订单中...';

            // 创建FormData对象
            const formData = new FormData();
            formData.append('customer_contact', '<EMAIL>'); // 这里可以让用户输入或使用默认值
            formData.append('product_id', currentProduct.id);
            formData.append('pay_type', currentPaymentType);

            // 调用本站的安全代理接口
            fetch('/index.php/Index/Utils/createOrder', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        currentOrderId = data.data.order_info.order_id;

                        // 更新订单信息
                        document.getElementById('orderId').textContent = currentOrderId;
                        document.getElementById('orderProduct').textContent = data.data.order_info.product_name;
                        document.getElementById('orderPrice').textContent = data.data.order_info.product_price;
                        document.getElementById('paymentMethod').textContent = currentPaymentType === 'wxpay' ? '微信' : '支付宝';

                        // 生成二维码
                        generateQRCode(data.data.payment_url);

                        // 关闭支付方式选择模态框，显示二维码模态框
                        closeModal('paymentModal');
                        showModal('qrModal');
                    } else {
                        alert('创建订单失败: ' + (data.message || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('创建订单错误:', error);
                    alert('创建订单失败，请重试');
                })
                .finally(() => {
                    confirmBtn.disabled = false;
                    confirmBtn.innerHTML = '确认支付方式';
                });
        }

        // 生成二维码
        function generateQRCode(url) {
            const qrContainer = document.getElementById('qrCode');
            qrContainer.innerHTML = '';

            try {
                const qr = qrcode(0, 'M');
                qr.addData(url);
                qr.make();

                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const modules = qr.getModuleCount();
                const cellSize = 300 / modules;

                canvas.width = 300;
                canvas.height = 300;

                // 设置背景色为白色
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, 300, 300);

                // 绘制二维码
                ctx.fillStyle = '#000000';
                for (let row = 0; row < modules; row++) {
                    for (let col = 0; col < modules; col++) {
                        if (qr.isDark(row, col)) {
                            ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
                        }
                    }
                }

                qrContainer.appendChild(canvas);
            } catch (error) {
                console.error('生成二维码失败:', error);
                qrContainer.innerHTML = '<p style="color: red;">二维码生成失败</p>';
            }
        }

        // 检查支付状态
        function checkPaymentStatus() {
            if (!currentOrderId || checkingPayment || orderProcessed) {
                return;
            }

            checkingPayment = true;
            const checkBtn = document.getElementById('checkPayment');
            const checkText = document.getElementById('checkText');

            checkBtn.disabled = true;
            checkText.innerHTML = '<span class="loading"></span>检查支付状态...';

            // 创建FormData对象
            const formData = new FormData();
            formData.append('order_id', currentOrderId);

            // 调用本站的安全代理接口
            fetch('/index.php/Index/Utils/checkPayment', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.already_processed) {
                        // 订单已经被处理过
                        orderProcessed = true;
                        showNotification(data.message || '该订单已经完成注册', 'warning');
                        checkBtn.style.display = 'none'; // 隐藏按钮
                        return;
                    }

                    if (data.status === 'success' && data.data && data.data.order_status === 'paid') {
                        // 支付成功，调用会员注册API
                        registerVip();
                    } else {
                        // 未支付
                        showNotification('未检测到支付，请确认支付后再试', 'warning');
                    }
                })
                .catch(error => {
                    console.error('检查支付状态错误:', error);
                    showNotification('检查支付状态失败，请重试', 'error');
                })
                .finally(() => {
                    checkingPayment = false;
                    if (!orderProcessed) {
                        checkBtn.disabled = false;
                        checkText.textContent = '我已支付';
                    }
                });
        }

        // 注册会员
        function registerVip() {
            if (!currentOrderId || !currentProduct || orderProcessed) {
                showErrorResult('订单信息缺失或已处理');
                return;
            }

            // 标记订单正在处理，防止重复点击
            orderProcessed = true;
            const checkBtn = document.getElementById('checkPayment');
            checkBtn.disabled = true;
            checkBtn.style.display = 'none';

            const productType = currentProduct.id === 85 ? '1' : '2';

            // 创建FormData对象
            const formData = new FormData();
            formData.append('order_id', currentOrderId);
            formData.append('product_type', productType);

            // 调用本站的安全代理接口
            fetch('/index.php/Index/Utils/registerVip', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    closeModal('qrModal');

                    if (data.code === 200) {
                        // 注册成功
                        showSuccessResult(data.token, data.reg_time, data.expire_time);
                    } else {
                        // 注册失败
                        showErrorResult(data.message || '注册失败');
                        // 如果是重复操作错误，保持orderProcessed为true
                        if (data.message && data.message.includes('重复操作')) {
                            orderProcessed = true;
                        } else {
                            orderProcessed = false; // 其他错误允许重试
                        }
                    }
                })
                .catch(error => {
                    console.error('注册会员错误:', error);
                    closeModal('qrModal');
                    showErrorResult('网络错误，请联系客服');
                    orderProcessed = false; // 网络错误允许重试
                });
        }

        // 显示成功结果
        function showSuccessResult(token, regTime, expireTime) {
            const resultBody = document.getElementById('resultBody');
            document.getElementById('resultTitle').textContent = '注册成功';

            resultBody.innerHTML = `
                <div class="success-icon">✅</div>
                <h3>恭喜！会员注册成功</h3>
                <div class="payment-info">
                    <strong>订单号:</strong>
                    <span class="order-id-container">
                        ${currentOrderId}
                        <button class="order-copy-btn" onclick="copyOrderId('${currentOrderId}')">复制</button>
                    </span><br>
                    <strong>注册时间:</strong> ${regTime}<br>
                    <strong>到期时间:</strong> ${expireTime}
                </div>
                <div class="token-display">
                    您的卡密: ${token}
                    <button class="copy-btn" onclick="copyToken('${token}')">复制</button>
                </div>
                <div class="warning-text">
                    ⚠️ 请谨慎保存您的卡密，丢失后无法找回！建议截图保存。
                </div>
                <button class="contact-service-btn" onclick="showContactModal()">
                    📞 联系客服
                </button>
            `;

            showModal('resultModal');
        }

        // 显示错误结果
        function showErrorResult(message) {
            const resultBody = document.getElementById('resultBody');
            document.getElementById('resultTitle').textContent = '注册失败';

            resultBody.innerHTML = `
                <div class="error-icon">❌</div>
                <h3>注册失败</h3>
                <div class="payment-info">
                    <strong>订单号:</strong>
                    <span class="order-id-container">
                        ${currentOrderId}
                        <button class="order-copy-btn" onclick="copyOrderId('${currentOrderId}')">复制</button>
                    </span><br>
                    <strong>错误信息:</strong> ${message}
                </div>
                <button class="contact-service-btn" onclick="showContactModal()">
                    � 联系客服处理
                </button>
            `;

            showModal('resultModal');
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'error' ? '#f44336' : type === 'warning' ? '#ff9800' : '#4CAF50'};
                color: white;
                padding: 15px 20px;
                border-radius: 5px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                font-weight: bold;
                max-width: 300px;
                animation: slideInRight 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 显示模态框
        function showModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            document.body.style.overflow = 'hidden';

            // 移动端优化：滚动到顶部
            if (window.innerWidth <= 768) {
                setTimeout(() => {
                    window.scrollTo(0, 0);
                }, 100);
            }
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            document.body.style.overflow = 'auto';

            // 如果关闭的是支付相关模态框，重置订单状态
            if (modalId === 'qrModal' || modalId === 'paymentModal') {
                // 不完全重置，保留当前订单信息，只重置处理状态
                checkingPayment = false;
            }
        }

        // 移动端优化：防止页面缩放
        document.addEventListener('gesturestart', function (e) {
            e.preventDefault();
        });

        // 移动端优化：改善触摸体验
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有按钮添加触摸反馈
            const buttons = document.querySelectorAll('button, .payment-option, .back-btn, .telegram-link');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.opacity = '0.8';
                });

                button.addEventListener('touchend', function() {
                    this.style.opacity = '1';
                });

                button.addEventListener('touchcancel', function() {
                    this.style.opacity = '1';
                });
            });

            // 移动端键盘弹出时的处理
            if (window.innerWidth <= 768) {
                const viewport = document.querySelector('meta[name=viewport]');
                if (viewport) {
                    let originalContent = viewport.content;

                    window.addEventListener('resize', function() {
                        if (window.innerHeight < window.innerWidth * 0.6) {
                            // 可能是键盘弹出
                            viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
                        } else {
                            // 键盘收起
                            viewport.content = originalContent;
                        }
                    });
                }
            }
        });

        // 点击模态框外部不关闭（根据需求）
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    // 对于支付二维码、结果模态框，点击外部不关闭
                    if (modal.id === 'qrModal' || modal.id === 'resultModal') {
                        return;
                    }
                    // 只有支付方式选择模态框可以点击外部关闭
                    if (modal.id === 'paymentModal') {
                        closeModal(modal.id);
                    }
                }
            });
        }

        // 复制Token功能
        function copyToken(token) {
            // 兼容性复制函数
            function fallbackCopyTextToClipboard(text) {
                const textArea = document.createElement("textarea");
                textArea.value = text;
                textArea.style.top = "0";
                textArea.style.left = "0";
                textArea.style.position = "fixed";
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                try {
                    const successful = document.execCommand('copy');
                    document.body.removeChild(textArea);
                    return successful;
                } catch (err) {
                    document.body.removeChild(textArea);
                    return false;
                }
            }

            const copyBtn = event.target;
            const originalText = copyBtn.textContent;

            // 尝试使用现代API，失败则使用兼容方法
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(token).then(() => {
                    copyBtn.textContent = '已复制';
                    copyBtn.classList.add('copied');

                    setTimeout(() => {
                        copyBtn.textContent = originalText;
                        copyBtn.classList.remove('copied');
                    }, 2000);

                    showNotification('卡密已复制到剪贴板', 'success');
                }).catch(err => {
                    console.error('现代API复制失败，尝试兼容方法:', err);
                    if (fallbackCopyTextToClipboard(token)) {
                        copyBtn.textContent = '已复制';
                        copyBtn.classList.add('copied');

                        setTimeout(() => {
                            copyBtn.textContent = originalText;
                            copyBtn.classList.remove('copied');
                        }, 2000);

                        showNotification('卡密已复制到剪贴板', 'success');
                    } else {
                        showNotification('复制失败，请手动复制', 'error');
                    }
                });
            } else {
                // 直接使用兼容方法
                if (fallbackCopyTextToClipboard(token)) {
                    copyBtn.textContent = '已复制';
                    copyBtn.classList.add('copied');

                    setTimeout(() => {
                        copyBtn.textContent = originalText;
                        copyBtn.classList.remove('copied');
                    }, 2000);

                    showNotification('卡密已复制到剪贴板', 'success');
                } else {
                    showNotification('复制失败，请手动复制', 'error');
                }
            }
        }

        // 复制订单号功能
        function copyOrderId(orderId) {
            // 兼容性复制函数
            function fallbackCopyTextToClipboard(text) {
                const textArea = document.createElement("textarea");
                textArea.value = text;
                textArea.style.top = "0";
                textArea.style.left = "0";
                textArea.style.position = "fixed";
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                try {
                    const successful = document.execCommand('copy');
                    document.body.removeChild(textArea);
                    return successful;
                } catch (err) {
                    document.body.removeChild(textArea);
                    return false;
                }
            }

            const copyBtn = event.target;
            const originalText = copyBtn.textContent;

            // 尝试使用现代API，失败则使用兼容方法
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(orderId).then(() => {
                    copyBtn.textContent = '已复制';
                    copyBtn.style.background = 'rgba(76, 175, 80, 0.1)';
                    copyBtn.style.borderColor = 'rgba(76, 175, 80, 0.3)';
                    copyBtn.style.color = '#4CAF50';

                    setTimeout(() => {
                        copyBtn.textContent = originalText;
                        copyBtn.style.background = 'rgba(102, 126, 234, 0.1)';
                        copyBtn.style.borderColor = 'rgba(102, 126, 234, 0.3)';
                        copyBtn.style.color = '#667eea';
                    }, 2000);

                    showNotification('订单号已复制到剪贴板', 'success');
                }).catch(err => {
                    console.error('现代API复制失败，尝试兼容方法:', err);
                    if (fallbackCopyTextToClipboard(orderId)) {
                        copyBtn.textContent = '已复制';
                        copyBtn.style.background = 'rgba(76, 175, 80, 0.1)';
                        copyBtn.style.borderColor = 'rgba(76, 175, 80, 0.3)';
                        copyBtn.style.color = '#4CAF50';

                        setTimeout(() => {
                            copyBtn.textContent = originalText;
                            copyBtn.style.background = 'rgba(102, 126, 234, 0.1)';
                            copyBtn.style.borderColor = 'rgba(102, 126, 234, 0.3)';
                            copyBtn.style.color = '#667eea';
                        }, 2000);

                        showNotification('订单号已复制到剪贴板', 'success');
                    } else {
                        showNotification('复制失败，请手动复制', 'error');
                    }
                });
            } else {
                // 直接使用兼容方法
                if (fallbackCopyTextToClipboard(orderId)) {
                    copyBtn.textContent = '已复制';
                    copyBtn.style.background = 'rgba(76, 175, 80, 0.1)';
                    copyBtn.style.borderColor = 'rgba(76, 175, 80, 0.3)';
                    copyBtn.style.color = '#4CAF50';

                    setTimeout(() => {
                        copyBtn.textContent = originalText;
                        copyBtn.style.background = 'rgba(102, 126, 234, 0.1)';
                        copyBtn.style.borderColor = 'rgba(102, 126, 234, 0.3)';
                        copyBtn.style.color = '#667eea';
                    }, 2000);

                    showNotification('订单号已复制到剪贴板', 'success');
                } else {
                    showNotification('复制失败，请手动复制', 'error');
                }
            }
        }

        // 复制当前订单号功能
        function copyCurrentOrderId() {
            if (currentOrderId) {
                copyOrderId(currentOrderId);
            }
        }

        // 显示联系客服模态框
        function showContactModal() {
            showModal('contactModal');
        }

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>