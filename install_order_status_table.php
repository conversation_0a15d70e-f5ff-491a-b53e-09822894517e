<?php
/**
 * 安装订单状态跟踪表
 * 运行此脚本来创建防重复支付所需的数据表
 */

// 设置应用目录
define('APP_PATH','./Application/');

// 引入ThinkPHP框架
require_once './ThinkPHP/ThinkPHP.php';

try {
    // 创建订单状态跟踪表的SQL
    $sql = "CREATE TABLE IF NOT EXISTS `nav_order_status` (
      `id` int NOT NULL AUTO_INCREMENT,
      `order_id` varchar(100) NOT NULL COMMENT '订单号',
      `product_id` varchar(20) DEFAULT NULL COMMENT '产品ID',
      `product_type` varchar(10) DEFAULT NULL COMMENT '产品类型(1=月度,2=永久)',
      `payment_status` enum('pending','paid','failed') DEFAULT 'pending' COMMENT '支付状态',
      `register_status` enum('pending','success','failed') DEFAULT 'pending' COMMENT '注册状态',
      `vip_token` varchar(255) DEFAULT NULL COMMENT '生成的VIP令牌',
      `reg_time` varchar(50) DEFAULT NULL COMMENT '注册时间',
      `expire_time` varchar(50) DEFAULT NULL COMMENT '过期时间',
      `client_ip` varchar(45) DEFAULT NULL COMMENT '客户端IP',
      `user_agent` text COMMENT '用户代理',
      `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      `processed_count` int DEFAULT 0 COMMENT '处理次数计数',
      `last_check_time` timestamp NULL DEFAULT NULL COMMENT '最后检查时间',
      PRIMARY KEY (`id`),
      UNIQUE KEY `uk_order_id` (`order_id`),
      KEY `idx_payment_status` (`payment_status`),
      KEY `idx_register_status` (`register_status`),
      KEY `idx_created_at` (`created_at`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COMMENT='订单状态跟踪表'";

    // 使用ThinkPHP的数据库操作
    $result = M()->execute($sql);
    
    if ($result !== false) {
        echo "✅ 订单状态跟踪表创建成功！\n";
        echo "表名: nav_order_status\n";
        echo "功能: 防止重复支付检测和注册\n";
        echo "\n";
        echo "🎉 安装完成！现在可以使用防重复支付功能了。\n";
    } else {
        echo "❌ 创建表失败，请检查数据库连接和权限。\n";
    }

} catch (Exception $e) {
    echo "❌ 安装过程中发生错误: " . $e->getMessage() . "\n";
    echo "请检查:\n";
    echo "1. 数据库连接配置是否正确\n";
    echo "2. 数据库用户是否有CREATE TABLE权限\n";
    echo "3. 数据库是否存在\n";
}

echo "\n";
echo "如果遇到问题，请手动执行 order_status_table.sql 文件中的SQL语句。\n";
?>
