<?php
/**
 * 测试防重复支付功能
 * 模拟订单创建、支付检查和注册流程
 */

// 设置应用目录
define('APP_PATH','./Application/');

// 引入ThinkPHP框架
require_once './ThinkPHP/ThinkPHP.php';

echo "🧪 开始测试防重复支付功能...\n\n";

// 测试订单ID
$testOrderId = 'TEST_ORDER_' . time();
echo "📝 测试订单号: {$testOrderId}\n\n";

try {
    // 1. 测试订单状态记录
    echo "1️⃣ 测试订单状态记录...\n";
    $orderStatusModel = M('order_status');
    
    $testData = array(
        'order_id' => $testOrderId,
        'product_id' => '85',
        'product_type' => '1',
        'payment_status' => 'pending',
        'register_status' => 'pending',
        'client_ip' => '127.0.0.1',
        'user_agent' => 'Test Agent',
        'processed_count' => 0
    );
    
    $result = $orderStatusModel->add($testData);
    if ($result) {
        echo "✅ 订单状态记录创建成功 (ID: {$result})\n\n";
    } else {
        echo "❌ 订单状态记录创建失败\n\n";
        exit(1);
    }

    // 2. 测试获取订单状态
    echo "2️⃣ 测试获取订单状态...\n";
    $orderStatus = $orderStatusModel->where(array('order_id' => $testOrderId))->find();
    if ($orderStatus) {
        echo "✅ 订单状态获取成功\n";
        echo "   - 支付状态: {$orderStatus['payment_status']}\n";
        echo "   - 注册状态: {$orderStatus['register_status']}\n";
        echo "   - 处理次数: {$orderStatus['processed_count']}\n\n";
    } else {
        echo "❌ 订单状态获取失败\n\n";
        exit(1);
    }

    // 3. 测试更新支付状态
    echo "3️⃣ 测试更新支付状态...\n";
    $updateResult = $orderStatusModel->where(array('order_id' => $testOrderId))->save(array(
        'payment_status' => 'paid',
        'last_check_time' => date('Y-m-d H:i:s')
    ));
    if ($updateResult !== false) {
        echo "✅ 支付状态更新成功\n\n";
    } else {
        echo "❌ 支付状态更新失败\n\n";
        exit(1);
    }

    // 4. 测试处理计数增加
    echo "4️⃣ 测试处理计数增加...\n";
    $incResult = $orderStatusModel->where(array('order_id' => $testOrderId))->setInc('processed_count');
    if ($incResult !== false) {
        echo "✅ 处理计数增加成功\n\n";
    } else {
        echo "❌ 处理计数增加失败\n\n";
        exit(1);
    }

    // 5. 测试注册状态更新
    echo "5️⃣ 测试注册状态更新...\n";
    $registerResult = $orderStatusModel->where(array('order_id' => $testOrderId))->save(array(
        'register_status' => 'success',
        'vip_token' => 'TEST_TOKEN_' . time(),
        'reg_time' => date('Y-m-d H:i:s'),
        'expire_time' => date('Y-m-d H:i:s', strtotime('+1 month'))
    ));
    if ($registerResult !== false) {
        echo "✅ 注册状态更新成功\n\n";
    } else {
        echo "❌ 注册状态更新失败\n\n";
        exit(1);
    }

    // 6. 测试重复操作检查
    echo "6️⃣ 测试重复操作检查...\n";
    $finalStatus = $orderStatusModel->where(array('order_id' => $testOrderId))->find();
    if ($finalStatus && $finalStatus['register_status'] === 'success') {
        echo "✅ 重复操作检查通过 - 订单已标记为成功注册\n";
        echo "   - 最终状态: 支付={$finalStatus['payment_status']}, 注册={$finalStatus['register_status']}\n";
        echo "   - VIP令牌: {$finalStatus['vip_token']}\n";
        echo "   - 处理次数: {$finalStatus['processed_count']}\n\n";
    } else {
        echo "❌ 重复操作检查失败\n\n";
        exit(1);
    }

    // 7. 清理测试数据
    echo "7️⃣ 清理测试数据...\n";
    $deleteResult = $orderStatusModel->where(array('order_id' => $testOrderId))->delete();
    if ($deleteResult) {
        echo "✅ 测试数据清理成功\n\n";
    } else {
        echo "⚠️ 测试数据清理失败，请手动删除订单号: {$testOrderId}\n\n";
    }

    echo "🎉 所有测试通过！防重复支付功能正常工作。\n\n";
    echo "📋 功能说明:\n";
    echo "   ✓ 订单状态跟踪\n";
    echo "   ✓ 支付状态更新\n";
    echo "   ✓ 注册状态标记\n";
    echo "   ✓ 重复操作防护\n";
    echo "   ✓ 处理次数统计\n\n";

} catch (Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "请检查数据库连接和表结构是否正确。\n";
    
    // 尝试清理测试数据
    try {
        M('order_status')->where(array('order_id' => $testOrderId))->delete();
        echo "🧹 测试数据已清理\n";
    } catch (Exception $cleanupError) {
        echo "⚠️ 清理测试数据失败: " . $cleanupError->getMessage() . "\n";
    }
}
?>
