<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>投诉订单功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 20px 0;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 5px;
        }
        .test-item h3 {
            margin-top: 0;
            color: #333;
        }
        .complaint-link {
            display: inline-block;
            padding: 12px 20px;
            background: #ff6b6b;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            transition: all 0.2s ease;
            box-shadow: 2px 2px 0 rgba(255, 107, 107, 0.3);
        }
        .complaint-link:hover {
            background: #ff5252;
            transform: translate(-1px, -1px);
            box-shadow: 3px 3px 0 rgba(255, 107, 107, 0.5);
        }
        .success {
            color: #4CAF50;
            font-weight: bold;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-left: 4px solid #2196F3;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 投诉订单功能测试</h1>
        
        <div class="info">
            <strong>📋 测试说明：</strong><br>
            本页面用于测试投诉订单功能是否正常工作。点击下方的投诉订单链接，应该会在新窗口中打开投诉页面。
        </div>

        <div class="test-item">
            <h3>1. 基础投诉链接测试</h3>
            <p>测试投诉订单链接是否可以正常访问：</p>
            <a href="https://cloudshop.qnm6.top/tousu.html" target="_blank" class="complaint-link">
                📋 投诉订单
            </a>
            <p><small>✅ 点击后应在新窗口打开投诉页面</small></p>
        </div>

        <div class="test-item">
            <h3>2. 联系客服模态框中的投诉链接</h3>
            <p>模拟联系客服模态框中的投诉订单按钮：</p>
            <div style="display: grid; gap: 16px; margin: 24px 0; max-width: 400px;">
                <a href="https://t.me/Dataso" target="_blank"
                   style="display: flex; align-items: center; justify-content: center; gap: 12px;
                          padding: 12px 20px; background: #e6f3ff;
                          border: 2px solid #0088cc; border-radius: 0;
                          color: #0088cc; text-decoration: none; font-weight: bold;
                          transition: all 0.2s ease;
                          box-shadow: 3px 3px 0 rgba(0, 136, 204, 0.3), inset 1px 1px 0 rgba(255, 255, 255, 0.8);">
                    <span style="font-size: 1.5em;">📱</span>
                    <span>Telegram客服: @Dataso</span>
                </a>

                <a href="https://cloudshop.qnm6.top/tousu.html" target="_blank"
                   style="display: flex; align-items: center; justify-content: center; gap: 12px;
                          padding: 12px 20px; background: #fff5f5;
                          border: 2px solid #ff6b6b; border-radius: 0;
                          color: #ff4757; text-decoration: none; font-weight: bold;
                          transition: all 0.2s ease;
                          box-shadow: 3px 3px 0 rgba(255, 107, 107, 0.3), inset 1px 1px 0 rgba(255, 255, 255, 0.8);">
                    <span style="font-size: 1.5em;">📋</span>
                    <span>投诉订单</span>
                </a>
            </div>
            <p><small>✅ 投诉订单按钮应该有红色主题样式</small></p>
        </div>

        <div class="test-item">
            <h3>3. 错误结果页面中的投诉链接</h3>
            <p>模拟注册失败时显示的投诉订单按钮：</p>
            <div style="display: grid; gap: 12px; margin-top: 20px; max-width: 300px;">
                <button style="background: linear-gradient(135deg, #ff6347 0%, #ff4500 100%);
                               color: white; border: 2px solid #ff4500; padding: 10px 20px;
                               border-radius: 0; font-size: 1em; font-weight: bold; cursor: pointer;">
                    📞 联系客服处理
                </button>
                <a href="https://cloudshop.qnm6.top/tousu.html" target="_blank" 
                   style="text-decoration: none; text-align: center; display: inline-block;
                          background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%);
                          border: 2px solid #ff4757; color: white; padding: 10px 20px;
                          border-radius: 0; font-size: 1em; font-weight: bold;">
                    📋 投诉订单
                </a>
            </div>
            <p><small>✅ 投诉订单按钮应该有不同的红色渐变样式</small></p>
        </div>

        <div class="test-item">
            <h3>4. 支付页面中的投诉提示</h3>
            <p>模拟支付页面中的投诉订单提示：</p>
            <div style="color: #8b0000; font-size: 0.9em; margin-top: 15px; padding: 12px;
                        background: #ffe4e1; border: 2px solid #ff6347; border-radius: 0;
                        font-weight: bold; box-shadow: 2px 2px 0 rgba(255, 99, 71, 0.3),
                        inset 1px 1px 0 rgba(255, 255, 255, 0.8);">
                <strong>💡 遇到支付问题？</strong><br>
                如果支付遇到问题或需要投诉订单，请点击下方按钮：
                <div style="margin-top: 10px;">
                    <a href="https://cloudshop.qnm6.top/tousu.html" target="_blank" 
                       style="display: inline-block; padding: 8px 16px; 
                              background: #ff6b6b; color: white; text-decoration: none; 
                              border-radius: 4px; font-weight: bold; font-size: 12px;
                              box-shadow: 2px 2px 0 rgba(255, 107, 107, 0.3);">
                        📋 投诉订单
                    </a>
                </div>
            </div>
            <p><small>✅ 投诉按钮应该嵌入在警告提示框中</small></p>
        </div>

        <div class="info">
            <strong>🎯 测试检查清单：</strong><br>
            ✅ 所有投诉订单链接都指向正确的URL<br>
            ✅ 链接在新窗口中打开<br>
            ✅ 按钮样式与页面风格一致<br>
            ✅ 移动端响应式设计正常<br>
            ✅ 悬停效果正常工作
        </div>

        <div style="text-align: center; margin-top: 30px; color: #666;">
            <p>测试完成后，请返回实际的会员购买页面验证功能。</p>
            <p><strong>投诉页面URL:</strong> <code>https://cloudshop.qnm6.top/tousu.html</code></p>
        </div>
    </div>

    <script>
        // 简单的点击统计
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'A' && e.target.href.includes('tousu.html')) {
                console.log('✅ 投诉订单链接被点击:', e.target.href);
                // 可以在这里添加统计代码
            }
        });
    </script>
</body>
</html>
